.edge-logo {
  max-width: 254px;
  max-height: 27px;
  @media (max-width: 800px) {
    max-width: 140px;
    max-height: 20px;
  }
}

.earnings-webinar-v1 {
  background: linear-gradient(90deg, #040419 0%, #063274 100%);

  .banner {
    .banner-content > div {
      display: flex;
      flex-direction: column-reverse;
      height: 100%;
      align-items: flex-start;
      padding-right: 1rem;
      @media (max-width: 800px) {
        padding-right: 0.5rem;
      }
    }
    h3 {
      color: white;
      font-family: Inter, sans-serif;
      font-size: 28px;
      font-weight: 700;
      line-height: 35px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      text-transform: uppercase;

      // @media (max-width: 1360px) {
      //   font-size: 28px;
      //   line-height: 28px;
      // }
      @media (max-width: 1443px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1270px) {
        font-size: 20px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 14px;
        line-height: 14px;
      }
    }
    p {
      color: #3588E5;
      font-size: 20px;
      line-height: 16px;
      font-weight: 700;
      text-transform: uppercase;
      padding-bottom: 0.25rem;
      max-width: fit-content;
      font-family: Inter, sans-serif;
      letter-spacing: 0.18em;
      text-align: left;
      height: 100%;

      @media (max-width:1332px) {
        font-size: 16px;
      }
      @media (max-width: 800px) {
        font-size: 10px;
        line-height: 10px;
        letter-spacing: 0.1em;
      }
    }
    .button-wrapper {
      align-items: center;
      justify-content: center;
      @media (max-width: 800px) {
        background-color: transparent;
      }
    }

    .banner-button {
      background-color: #1A81FA;
      padding: 1rem !important;

      color: white;
      white-space: nowrap;
      text-transform: uppercase;
      border-radius: 2px !important;
      font-family: Inter, sans-serif;
      font-size: 20px;
      font-weight: 800;
      line-height: 26px;
      letter-spacing: 0.08em;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      height: 80%;

      &:hover {
        background-color:#3a74da;
      }

      @media (max-width: 1000px) {
        font-size: 16px;
        line-height: 20px
      }

      @media(max-width: 800px) {
        font-size: 12px;
        line-height: 14px;
        white-space: wrap;
        text-align: center;
        margin-left: 1rem;
      }
    }
  }

  .banner-bg-end {
    background-image: url('/next-assets/images/banners/erx-end-bg.png');
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
    width: 944px;
    height: 80px;
  }
}

.pattern-trader-pro-v1, .pattern-trader-pro-v2 {
  background: #000000;
  position: relative;
  overflow: hidden;

  .banner {
    position: relative;
    z-index: 10;
    font-family: Inter, sans-serif;

    .banner-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      position: relative;
      z-index: 20;

      .live-event-section {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        position: relative;

        .live-event {
          text-align: center;
          font-family: Inter, sans-serif;
          font-size: 10px;
          line-height: 12px;
          margin-top: 6px;
          letter-spacing: 0.25em;
          font-weight: 500;
          text-transform: uppercase;
          opacity: 0.8;
          white-space: nowrap;

          @media (max-width: 800px) {
            display: none;
          }
        }

        .live-event-icon {
          width: 20px;
          height: 20px;
          margin-left: -5px;
          background-image: url('/next-assets/images/banners/pattern-trader-pro-icon-v1.png');
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          border-radius: 50%;
          box-shadow: 0 0 0 #00f3ff59, 0 0 12px #00f3ff66, 0 0 12px #00f3ffb3;

          @media (max-width: 800px) {
            width: 16px;
            height: 16px;
          }
        }

        .divider-line {
          width: 1px;
          height: 70px;
          background: #ffffff40;
          margin-left: 1rem;

          @media (max-width: 800px) {
            margin-left: 0.5rem;
          }
        }
      }

      .main-title {
        font-family: Inter, sans-serif;
        font-size: 33px;
        line-height: 1.14;
        font-weight: 400;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.25rem;

        .title-part-1 {
          color: #ffffff;
          font-weight: 700;
        }

        .title-part-2 {
          color: #00f3ff;
          font-weight: 700;
          font-style: italic;
          margin: 0 4px;
        }

        .title-part-3 {
          color: #ffffff;
          font-weight: 700;
        }

        @media (max-width: 1443px) {
          font-size: 28px;
        }
        @media (max-width: 1270px) {
          font-size: 24px;
        }
        @media (max-width: 1000px) {
          font-size: 20px;
        }
        @media (max-width: 800px) {
          font-size: 16px;
          line-height: 1.2;
        }
      }
    }

    .description {
      font-family: Inter, sans-serif;
      font-weight: 600;
      @media (max-width: 800px) {
        display: none;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 30;

      .banner-button {
        border-radius: 0.75rem;
        border: 2px solid rgba(255, 255, 255, 0.32);
        background: radial-gradient(45.93% 147.72% at 50% -12.27%, rgba(0, 243, 255, 0.70) 0%, rgba(32, 177, 185, 0.70) 100%), radial-gradient(321.78% 334.63% at 50% 125.45%, #00F3FF 0%, #2AE1EA 100%), linear-gradient(90deg, #00F3FF 0%, #00F3FF 20%, #00F3FF 80%, #00F3FF 100%);
        box-shadow: 0px 12px 44px 0px rgba(252, 214, 255, 0.34) inset, 0px -8px 44px 0px rgba(0, 0, 0, 0.34) inset;
        color: #ffffff;
        text-align: center;
        font-family: Inter, sans-serif;
        font-size: 17px;
        line-height: 19px;
        font-weight: 700;
        text-transform: uppercase;
        white-space: nowrap;
        padding: 0 !important;
        width: 213px;
        height: 44.31px;
        display: flex;
        align-items: center;
        justify-content: center;

        @media (max-width: 1000px) {
          font-size: 15px;
          width: 180px;
          height: 38px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
          line-height: 14px;
          white-space: wrap;
          text-align: center;
          width: 150px;
          height: 32px;
        }
      }
    }
  }

  .banner-bg-end {
    position: absolute;
    right: 0;
    top: 0;
    width: 944px;
    height: 80px;
    background-image: url('/next-assets/images/banners/pattern-trader-pro-bg-v1.png');
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
    z-index: 1;

    -webkit-mask: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.1) 15%,
      rgba(0, 0, 0, 0.5) 30%,
      rgba(0, 0, 0, 0.8) 50%,
      rgba(0, 0, 0, 1) 70%,
      rgba(0, 0, 0, 1) 100%
    );
    mask: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.1) 15%,
      rgba(0, 0, 0, 0.5) 30%,
      rgba(0, 0, 0, 0.8) 50%,
      rgba(0, 0, 0, 1) 70%,
      rgba(0, 0, 0, 1) 100%
    );

    @media (max-width: 1200px) {
      right: -100px !important;
    }
    @media (max-width: 800px) {
      right: -200px !important;
      opacity: 0.8;
    }
  }
}

.pattern-trader-pro-v3 {
  background: #0e1743;
  position: relative;
  overflow: hidden;

  .banner {
    position: relative;
    z-index: 10;
    font-family: Inter, sans-serif;

    .banner-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      position: relative;
      z-index: 20;

      .live-event-section {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        position: relative;

        .live-event {
          color: #FFFFFF;
          text-align: center;
          font-family: Inter, sans-serif;
          font-size: 10px;
          line-height: 12px;
          margin-top: 6px;
          letter-spacing: 0.25em;
          font-weight: 500;
          text-transform: uppercase;
          opacity: 0.8;
          white-space: nowrap;

          @media (max-width: 800px) {
            display: none;
          }
        }

        .live-event-icon {
          width: 25px;
          height: 25px;
          margin-left: -5px;
          background-image: url('/next-assets/images/banners/pattern-trader-pro-icon-v3.png');
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          border-radius: 50%;
          box-shadow: 0 0 0 #d34dff, 0 0 16px #bc24ed, 0 0 14px #7b1f99;

          @media (max-width: 800px) {
            width: 16px;
            height: 16px;
          }
        }

        .divider-line {
          width: 1px;
          height: 70px;
          background: #ffffff40;
          margin-left: 1rem;

          @media (max-width: 800px) {
            margin-left: 0.5rem;
          }
        }
      }

      .main-title {
        font-family: Inter, sans-serif;
        font-size: 33px;
        line-height: 1.14;
        font-weight: 400;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.25rem;
        font-weight: 700;

        .title-part-1 {
          color: #BF36EC;
        }

        .title-part-2 {
          color: #ffffff;
        }

        .title-part-3 {
          color: #ffffff;
        }

        @media (max-width: 1443px) {
          font-size: 28px;
        }
        @media (max-width: 1270px) {
          font-size: 24px;
        }
        @media (max-width: 1000px) {
          font-size: 20px;
        }
        @media (max-width: 800px) {
          font-size: 16px;
          line-height: 1.2;
        }
      }
    }

    .description {
      font-family: Inter, sans-serif;
      font-weight: 600;
      @media (max-width: 800px) {
        display: none;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 30;

      .banner-button {
        border-radius: 0.75rem;
        border: 2px solid rgba(255, 255, 255, 0.32);
        background: radial-gradient(45.93% 147.72% at 50% -12.27%, rgba(192, 62, 235, 0.70) 0%, rgba(255, 255, 255, 0.00) 71.15%), radial-gradient(321.78% 334.63% at 50% 125.45%, #8F22B3 0%, rgba(192, 62, 235, 0.00) 86.54%), linear-gradient(90deg, #BF36EC 0%, #C03EEB 20%, #C03EEB 57.21%, #BF36EC 100%);
        box-shadow: 0px 12px 44px 0px rgba(252, 214, 255, 0.34) inset, 0px -8px 44px 0px rgba(0, 0, 0, 0.34) inset;
        color: #ffffff;
        text-align: center;
        font-family: Inter, sans-serif;
        font-size: 17px;
        line-height: 19px;
        font-weight: 700;
        text-transform: uppercase;
        white-space: nowrap;
        padding: 0 !important;
        width: 213px;
        height: 44.31px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        @media (max-width: 1000px) {
          font-size: 15px;
          width: 180px;
          height: 38px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
          line-height: 14px;
          white-space: wrap;
          text-align: center;
          width: 150px;
          height: 32px;
        }
      }
    }
  }

  .banner-bg-end {
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
    height: 80px;
    background-image: url('/next-assets/images/banners/pattern-trader-pro-bg-v3.png');
    background-position: right;
    background-size: cover;
    background-repeat: no-repeat;
    z-index: 1;
    opacity: 0.4;
    filter: blur(1px);

    -webkit-mask: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.1) 15%,
      rgba(0, 0, 0, 0.5) 30%,
      rgba(0, 0, 0, 0.8) 50%,
      rgba(0, 0, 0, 1) 70%,
      rgba(0, 0, 0, 1) 100%
    );
    mask: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.1) 15%,
      rgba(0, 0, 0, 0.5) 30%,
      rgba(0, 0, 0, 0.8) 50%,
      rgba(0, 0, 0, 1) 70%,
      rgba(0, 0, 0, 1) 100%
    );

    @media (max-width: 1200px) {
      right: -100px !important;
    }
    @media (max-width: 800px) {
      right: -200px !important;
      opacity: 0.8;
    }
  }
}

.pattern-trader-pro-v4 {
  background: #0e1743;
  position: relative;
  overflow: hidden;

  .banner {
    position: relative;
    z-index: 10;
    font-family: Inter, sans-serif;

    .banner-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      position: relative;
      z-index: 20;
      height: 100%;

      .main-title {
        font-family: Inter, sans-serif;
        font-size: 33px;
        line-height: 1.14;
        font-weight: 400;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.25rem;
        font-weight: 700;

        .title-part-1 {
          color: #ffffff;
        }

        .title-part-2 {
          color: #BF36EC;
        }

        .title-part-3 {
          color: #ffffff;
        }

        @media (max-width: 1443px) {
          font-size: 28px;
        }
        @media (max-width: 1270px) {
          font-size: 24px;
        }
        @media (max-width: 1000px) {
          font-size: 20px;
        }
        @media (max-width: 800px) {
          font-size: 16px;
          line-height: 1.2;
        }
      }
    }

    .description {
      font-family: Inter, sans-serif;
      font-weight: 600;
      @media (max-width: 800px) {
        display: none;
      }
      @media (max-width: 1100px) {
        font-size: 12px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 30;

      .banner-button {
        border-radius: 0.75rem;
        border: 2px solid rgba(255, 255, 255, 0.32);
        background: radial-gradient(45.93% 147.72% at 50% -12.27%, rgba(192, 62, 235, 0.70) 0%, rgba(255, 255, 255, 0.00) 71.15%), radial-gradient(321.78% 334.63% at 50% 125.45%, #8F22B3 0%, rgba(192, 62, 235, 0.00) 86.54%), linear-gradient(90deg, #BF36EC 0%, #C03EEB 20%, #C03EEB 57.21%, #BF36EC 100%);
        box-shadow: 0px 12px 44px 0px rgba(252, 214, 255, 0.34) inset, 0px -8px 44px 0px rgba(0, 0, 0, 0.34) inset;
        color: #ffffff;
        text-align: center;
        font-family: Inter, sans-serif;
        font-size: 17px;
        line-height: 19px;
        font-weight: 700;
        text-transform: uppercase;
        white-space: nowrap;
        padding: 0 !important;
        width: 213px;
        height: 44.31px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        @media (max-width: 1000px) {
          font-size: 15px;
          width: 180px;
          height: 38px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
          line-height: 14px;
          white-space: wrap;
          text-align: center;
          width: 150px;
          height: 32px;
        }
      }
    }
  }

  .tom-gentile-image {
    width: 80px;
    height: 70px;
    background-image: url('/next-assets/images/banners/tom_gentile_v4.png');
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    margin-left: 1rem;
    flex-shrink: 0;
    margin-top: auto;

    @media (max-width: 800px) {
      display: none;
    }
  }

  .banner-bg-end {
    position: absolute;
    right: 0;
    top: 0;
    width: 944px;
    height: 80px;
    background-image: url('/next-assets/images/banners/pattern-trader-pro-bg-v3.png');
    background-position: right;
    background-size: cover;
    background-repeat: no-repeat;
    z-index: 1;
    opacity: 0.4;
    filter: blur(1px);

    -webkit-mask: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.1) 15%,
      rgba(0, 0, 0, 0.5) 30%,
      rgba(0, 0, 0, 0.8) 50%,
      rgba(0, 0, 0, 1) 70%,
      rgba(0, 0, 0, 1) 100%
    );
    mask: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.1) 15%,
      rgba(0, 0, 0, 0.5) 30%,
      rgba(0, 0, 0, 0.8) 50%,
      rgba(0, 0, 0, 1) 70%,
      rgba(0, 0, 0, 1) 100%
    );

    @media (max-width: 1200px) {
      right: -100px !important;
    }
    @media (max-width: 800px) {
      right: -200px !important;
      opacity: 0.8;
      background-size: cover;
    }
  }
}

@keyframes glowPurple {
  from {
    box-shadow: 0 0 15px rgba(191, 54, 236, 0.4), 0 0 30px rgba(191, 54, 236, 0.3), 0 0 45px rgba(191, 54, 236, 0.2);
  }
  to {
    box-shadow: 0 0 25px rgba(191, 54, 236, 0.6), 0 0 50px rgba(191, 54, 236, 0.4), 0 0 75px rgba(191, 54, 236, 0.3);
  }
}

.lead-gen-v3 {
  background: linear-gradient(270deg, #00203B 0%, #001120 100%);

  .banner {
    p {
      display: flex;
      flex-direction: row;
      gap: 0.5rem;
      color: white;
      font-size: 32px;
      line-height: 32px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      text-transform: uppercase;
      @media (max-width: 1320px) {
        font-size: 28px;
        line-height: 28px;
      }
      @media (max-width: 1240px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1100px) {
        font-size: 20px;
      }
      @media (max-width: 900px) {
        flex-direction: column;
        font-size: 16px;
        line-height: 14px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      height: 100%;

      .banner-button {
        background-color: #3F83F8;
        font-size: 24px;
        line-height: 28px;
        white-space: nowrap;
        text-transform: uppercase;

        @media (max-width: 1400px) {
          font-size: 20px;
          line-height: 24px;
        }
        @media (max-width: 800px) {
          font-size: 14px;
          line-height: 16px;
          white-space: wrap;
        }
      }
    }
  }

  .banner-bg-end {
    background-image: url('/next-assets/images/banners/leadgen/lead-gen-v3.png');
    position: absolute;
    background-size: cover;
    background-repeat: no-repeat;
    width: 673px;
    height: 80px;
    top: 0;
    right: 0;
    background-position: right;

    @media (max-width: 800px) {
      right: -400px !important;
    }
  }
}

.lead-gen-v4 {
  background: #0F1F3D;

  .banner {
    p {
      display: flex;
      flex-direction: row;
      gap: 0.5rem;
      color: white;
      font-size: 30px;
      line-height: 32px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      text-transform: capitalize;
      @media (max-width: 1320px) {
        font-size: 28px;
        line-height: 28px;
      }
      @media (max-width: 1240px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1100px) {
        font-size: 20px;
      }
      @media (max-width: 900px) {
        flex-direction: column;
        font-size: 16px;
        line-height: 16px;
        gap: 0.25rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      height: 100%;

      .banner-button {
        background-color: #3F83F8;
        font-size: 24px;
        line-height: 28px;
        white-space: nowrap;
        text-transform: uppercase;

        @media (max-width: 1400px) {
          font-size: 20px;
          line-height: 24px;
        }
        @media (max-width: 800px) {
          font-size: 14px;
          line-height: 16px;
          white-space: wrap;
        }
      }
      .banner-button-bg {
        background-image: url('/next-assets/images/banners/leadgen/lead-gen-v4b.png');
        position: absolute;
        background-size: cover;
        background-repeat: no-repeat;
        width: 307px;
        height: 80px;
        top: 0;
        left: -210px;
      }
    }
  }

}

.matt-trial-v5 {
  background-color: #031C30;

  .banner {
    .banner-content div {
      display: flex;
      flex-direction: column-reverse;
      align-items: flex-start;
      justify-content: flex-start;
      margin-right: 2rem;
    }

    p {
      color: white;
      font-size: 30px;
      font-weight: 700;
      line-height: 28px;
      font-family: Inter, sans-serif;
      text-transform: uppercase;

      @media (max-width: 1380px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1100px) {
        font-size: 20px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 14px;
        line-height: 18px;
      }
    }

    h3 {
      color: white;
      font-family: Inter, sans-serif;
      font-weight: 500;
      font-size: 14px;
      line-height: 14px;
      letter-spacing: -2%;
      text-transform: capitalize;
      margin-top: 0.5rem;
      @media (max-width: 1200px) {
        font-size: 12px;
        line-height: 12px;
      }
      @media (max-width: 1100px) {
        font-size: 10px;
        line-height: 10px;
      }
      @media (max-width: 800px) {
        display: none;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;
      margin-left: 2rem;

      @media (max-width: 800px) {
        margin-left: 1rem;
      }

      .banner-button {
        position: relative;
        border-radius: 4px;
        background: linear-gradient(90deg, #479CFF 0%, #2B5E99 100%);
        font-size: 22px;
        white-space: nowrap;
        font-weight: 800;
        height: 70%;

        @media (max-width: 1300px) {
          font-size: 18px;
          padding: 1rem 1rem !important;
        }

        @media (max-width: 1100px) {
          font-size: 16px;
        }

        @media (max-width: 800px) {
          padding: 0.5rem !important;
          white-space: wrap;
          font-size: 12px;
          min-width: 80px;
        }
      }
      .banner-button::before {
        content: "";
        position: absolute;
        inset: 0;
        padding: 2px;
        border-radius: 4px;
        background: linear-gradient(to right, #FFFFFF, #2B5E99);
        -webkit-mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        z-index: -1;
      }

      .banner-button-bg {
        position: absolute;
        background-image: url('/next-assets/images/banners/matt/matt-trial-v5.png');
        height: 80px;
        width: 682px;
        left: 0;
        background-position: right;
        background-size: contain;
        margin-left: -360px;
        @media (max-width: 1100px) {
          margin-left: -340px;
        }

        @media (max-width: 800px) {
          margin-left: -320px;
        }
      }
    }
  }
}

.matt-trial-v6 {
  background-color: #0F1F3D;
  background-image: url('/next-assets/images/banners/matt/matt-trial-v6.png');
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;


  .banner {
    .banner-content div {
      display: flex;
    }

    p {
      color: white;
      font-size: 34px;
      font-weight: 700;
      line-height: 28px;
      font-family: Inter, sans-serif;
      text-transform: uppercase;

      @media (max-width: 1300px) {
        font-size: 30px;
        line-height: 22px;
      }
      @media (max-width: 1140px) {
        font-size: 28px;
        line-height: 28px;
      }
      @media (max-width: 1000px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 18px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;
      margin-left: 1rem;

      .banner-button {
        position: relative;
        border-radius: 4px;
        font-size: 22px;
        font-weight: 800;
        height: 70%;
        text-transform: uppercase;
        background-color: #479CFF;

        @media (max-width: 1300px) {
          font-size: 18px;
        }

        @media (max-width: 800px) {
          font-size: 14px;
          height: fit-content
        }
      }
    }
  }
}

.edge-v19 {
  background-color: #0F1F3D;
  background-image: url('/next-assets/images/banners/edge/edge-v19-bg.png');
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    p {
      display: flex;
      flex-direction: row;
      gap: 1rem;
      align-items: center;
      color: white;
      font-size: 32px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      margin-right: 1rem;
      text-align: center;
      height: 100%;

      @media (max-width: 1470px) {
        font-size: 28px;
        line-height: 26px;
      }

      @media (max-width: 1280px) {
        font-size: 24px;
        line-height: 22px;
      }

      @media (max-width: 1140px) {
        font-size: 20px;
        line-height: 22px;
      }

      @media (max-width: 1000px) {
        flex-wrap: wrap;
        text-align: left;
        gap: 0.25rem;
        font-size: 16px;
        line-height: 18px;
      }
      @media (max-width: 800px) {
        font-size: 14px;
        line-height: 16px;
        margin-right: 0.5rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        font-size: 22px;
        font-family: Inter, sans-serif;
        border-radius: 21px !important;
        text-transform: uppercase;
        background: linear-gradient(90deg, #2172FF 0%, #479CFF 100%);
        border: 0.99px solid #479CFF;
        font-family: Inter;
        font-weight: 800;
        font-size: 20.06px;
        line-height: 24px;
        letter-spacing: 2%;
        text-align: center;
        text-transform: uppercase;


        &:hover {
          background-color:#3a74da;
        }

        @media (max-width: 1100px) {
          font-size: 18px;
        }

        @media (max-width: 800px) {
          border-radius: 8px !important;
          white-space: wrap;
          font-size: 12px;
          max-width: 180px;
        }
      }
    }
  }
}

.edge-v20 {
  background: linear-gradient(180deg, #FFFFFF 0%, #E3E3E3 100%);

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    h3 {
      background: linear-gradient(180deg, #192940 0%, #143B71 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;

      font-family: Inter;
      font-weight: 800;
      font-size: 38px;
      line-height: 40px;
      letter-spacing: -2%;
      vertical-align: middle;
      text-transform: uppercase;

      @media screen and (max-width: 1300px) {
        font-size: 32px;
        line-height: 32px;
      }

      @media (max-width: 1140px) {
        font-size: 28px;
        line-height: 28px;
      }

      @media (max-width: 800px) {
        font-size: 20px;
        line-height: 18px;
        margin-right: 0.5rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        font-family: Inter, sans-serif;
        font-weight: 800;
        font-size: 20.06px;
        line-height: 24px;
        letter-spacing: 2%;
        text-align: center;
        text-transform: uppercase;
        background-color: #3F83F8;

        &:hover {
          background-color:#3a74da;
        }

        @media(max-width: 1575px) {
          margin-right: 40px;
        }

        @media (max-width: 1100px) {
          font-size: 18px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
          margin-right: 30px;
          height: fit-content;
          padding: 0.5rem !important;
          white-space: nowrap;
        }
      }
    }
  }

  .banner-bg-end {
    background-image: url('/next-assets/images/banners/mrd-flag.png');
    background-position: right;
    background-size: cover;
    background-repeat: no-repeat;
    width: 60px;
    height: 100%;
    @media (max-width: 800px) {
      margin-left: -20px;
    }
  }
}

.edge-mwd-v1 {
  background-image: url('/next-assets/images/banners/edge/mwd-bg.png');
  background-position: center;
  background-size: contain;
  width: 100%;

  .banner {
    margin: 0.25rem auto !important;
    border-radius: 4px;
    background-color: #081A3C;
    padding: 0.5rem;
    height: 90% !important;
    @media (max-width:1410px) {
      margin: 0.25rem !important;
    }

    .content {
      display: flex;
      flex-direction: column-reverse;
      align-items: flex-start;
      justify-content: flex-start;
      gap: 0.5rem;
      @media(max-width: 800px) {
        gap: 0.25rem;
      }
    }

    h3 {
      font-family: Inter, sans-serif;
      font-weight: 800;
      font-size: 32px;
      line-height: 30px;
      letter-spacing: -2%;
      vertical-align: middle;
      text-transform: uppercase;
      color: white;

      @media (max-width:1410px) {
        font-size: 28px;
      }

      @media (max-width: 1200px) {
        font-size: 24px;
        line-height: 24px;
      }
      @media (max-width: 1110px) {
        font-size: 20px;
        line-height: 18px;
      }

      @media (max-width: 800px) {
        font-size: 14px;
        line-height: 16px;
        margin-right: 0.5rem;
      }
    }

    p {
      color: #68A5FF;
      font-family: Inter, sans-serif;
      font-weight: 700;
      font-size: 16px;
      line-height: 9px;
      letter-spacing: 20%;
      text-transform: uppercase;

      @media (max-width: 800px) {
        font-size: 12px;
        line-height: 14px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        font-family: Inter, sans-serif;
        font-weight: 800;
        font-size: 20.06px;
        line-height: 24px;
        letter-spacing: 2%;
        text-align: center;
        text-transform: uppercase;
        background-color: #FF1313;
        white-space: nowrap;

        &:hover {
          background-color:#3a74da;
        }

        @media (max-width: 1100px) {
          font-size: 18px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
          line-height: 14px;
          height: fit-content;
          padding: 0.5rem !important;
          white-space: wrap;
        }
      }
    }
  }

}

.edge-mwd-v2 {
  background-image: url('/next-assets/images/banners/edge/mdw-v2-bg.png');
  background-position: left;
  background-size: cover;
  background-repeat: no-repeat;
  width: 100%;
  background-color: #051629;

  .banner {
    .content {
      display: flex;
      flex-direction: column-reverse;
      align-items: flex-start;
      justify-content: flex-start;
      gap: 0.5rem;
      @media(max-width: 800px) {
        gap: 0.25rem;
      }
    }

    h3 {
      font-family: Inter, sans-serif;
      font-weight: 700;
      font-size: 36px;
      line-height: 30px;
      vertical-align: middle;
      color: white;

      @media (max-width: 1400px) {
        font-size: 32px;
        line-height: 28px;
      }

      @media (max-width: 1250px) {
        font-size: 24px;
        line-height: 24px;
      }
      @media (max-width: 1110px) {
        font-size: 20px;
        line-height: 18px;
      }

      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 16px;
        margin-right: 0.5rem;
      }
    }

    p {
      font-family: Bebas Neue, Impact, 'Arial Narrow Bold', sans-serif;
      font-weight: 400;
      font-size: 24px;
      line-height: 14px;
      letter-spacing: 0.1em;
      text-transform: uppercase;

      @media (max-width: 1200px) {
        font-size: 18px;
      }
      @media (max-width: 800px) {
        font-size: 14px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        font-family: Inter, sans-serif;
        font-weight: 800;
        font-size: 20.06px;
        line-height: 24px;
        letter-spacing: 2%;
        text-align: center;
        text-transform: uppercase;
        background-color: #FF1313;
        white-space: nowrap;
        height: 80%;

        @media (max-width: 1100px) {
          font-size: 18px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
          line-height: 14px;
          height: fit-content;
          padding: 0.5rem !important;
          white-space: wrap;
        }
      }
    }
  }

}

.lead-gen-v6 {
  background-color: #0F1F3D;
  background-image: url('/next-assets/images/banners/leadgen/lead-gen-v6.png');
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;

  .banner {
    p {
      color: white;
      font-size: 32px;
      font-weight: 700;
      line-height: 28px;
      font-family: Inter, sans-serif;
      text-transform: uppercase;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      gap: 0.5rem;
      align-items: center;
      margin-top: 0.25rem;

      @media (max-width: 1440px) {
        font-size: 30px;
        line-height: 22px;
      }
      @media (max-width: 1340px) {
        font-size: 26px;
        line-height: 20px;
      }
      @media (max-width: 1200px) {
        font-size: 22px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        gap: 0;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;
      margin-left: 2rem;

      @media (max-width: 800px) {
        margin-left: 1rem;
      }

      .banner-button {
        position: relative;
        border-radius: 4px;
        font-size: 22px;
        font-weight: 700;
        height: 70%;
        white-space: nowrap;
        background-color: #3F80F8;
        text-transform: uppercase;

        @media (max-width: 1400px) {
          font-size: 20px;
        }

        @media (max-width: 1200px) {
          font-size: 16px;
          white-space: wrap;
          line-height: 14px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
          height: fit-content;
          min-height: 70%;
        }
      }
    }
  }
}

