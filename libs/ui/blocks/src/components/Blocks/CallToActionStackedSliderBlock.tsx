import React from 'react';
import { ProductBasedCTA, ProductBasedCTAProps, ProductBasedCTALayout } from '@benzinga/ui';
import { extractImageSize } from '@benzinga/image';
import styled from '@benzinga/themetron';
import { Carousel } from '@benzinga/ui';
import Hooks from '@benzinga/hooks';
import { SplideStyle } from '@benzinga/ui';
import { sanitizeHTML } from '@benzinga/frontend-utils';

export interface CallToActionStackedSliderBlockProps {
  display: ProductBasedCTALayout;
  groups?: ProductBasedCTAProps[];
  social_proofing?: string;
}

export const CallToActionStackedSliderBlock: React.FC<CallToActionStackedSliderBlockProps> = ({
  display,
  groups,
  social_proofing,
}) => {
  // const image = extractImageSize(campaign?.image);

  const ref = React.useRef<HTMLDivElement>(null);
  const { width } = Hooks.useRefElementSize(ref);
  const perPage = width <= 500 ? 1 : width <= 720 ? 2 : 3;
  if (groups && groups?.length === 0) return;

  return (
    <CallToActionCarouselWrapper ref={ref}>
      {social_proofing && (
        <div className="flex items-end justify-end mb-2">
          <div
            className="text-right text-sm bg-gray-200 rounded px-2 py-1"
            dangerouslySetInnerHTML={{ __html: sanitizeHTML(social_proofing) }}
          />
        </div>
      )}
      {display === 'vertical' ? (
        <div className={`cta-stacked ${SplideStyle.splide_slidewrap}`}>
          <Carousel
            component={campagin => {
              const image = extractImageSize(campagin?.image);
              return (
                <ProductBasedCTA
                  button_text={campagin.button_text}
                  description={campagin.description}
                  image_url={image?.url}
                  layout={display}
                  review={campagin.review}
                  review_url={campagin.review_url}
                  title={campagin.title}
                  url={campagin.url}
                />
              );
            }}
            items={groups as any[]}
            options={{
              autoWidth: false,
              breakpoints: {
                600: {
                  destroy: true,
                },
                750: {
                  perPage: 1,
                },
                900: {
                  perPage: 2,
                },
              },
              focus: groups && groups.length > 2 ? 'center' : 0,
              perPage,
            }}
            showArrowsOnlyOnOverflow={true}
            showPagination={true}
          />
        </div>
      ) : (
        <>
          <div className={`flex flex-col md:gap-3 gap-0`}>
            {groups &&
              groups.map((campaign, index) => {
                const image = extractImageSize(campaign?.image);
                return (
                  <div className={`basis-full`} key={index}>
                    <ProductBasedCTA
                      button_text={campaign.button_text}
                      description={campaign.description}
                      highlight_list={campaign?.highlight_list ?? []}
                      image_url={image?.url}
                      layout={display}
                      review={campaign?.review ?? 0}
                      review_url={campaign?.review_url ?? ''}
                      title={campaign.title}
                      url={campaign.url}
                    />
                  </div>
                );
              })}
          </div>
        </>
      )}
    </CallToActionCarouselWrapper>
  );
};

const CallToActionCarouselWrapper = styled.div`
  .call-to-action-wrapper {
    .cta-button {
      button {
        font-size: 1rem;
        padding: 0.75rem 0.65rem;
      }
    }
  }
`;
